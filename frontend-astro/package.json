{"name": "frontend-astro", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro check && astro build", "preview": "astro preview", "astro": "astro", "install": "pnpm install", "update": "pnpm update"}, "packageManager": "pnpm@8.15.6", "dependencies": {"@astrojs/react": "^4.3.0", "@astrojs/tailwind": "^6.0.2", "@heroicons/react": "^2.2.0", "@react-three/drei": "^10.3.0", "@react-three/fiber": "^9.1.2", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.81.2", "@tanstack/react-query-devtools": "^5.81.5", "@telegram-apps/sdk-react": "^3.3.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "astro": "^5.10.1", "axios": "^1.10.0", "crypto-js": "^4.2.0", "framer-motion": "^12.20.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "tailwindcss": "^3.4.17", "three": "^0.177.0", "zustand": "^5.0.5"}}