/**
 * @file HomeTabContent.tsx
 * @description Enhanced cyberpunk gaming dashboard home with stunning design
 * Interactive React island with proper styling and mock data for now
 */

import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaCoins, FaBolt, FaUsers, FaWallet, FaShieldAlt, FaRocket, FaGamepad, FaChartLine } from 'react-icons/fa';

// Types
interface HomeTabContentProps {
  onTabChange?: (tab: string) => void;
}

// Simple currency formatter
const formatCurrency = (amount: number): string => {
  return `$${amount.toFixed(2)}`;
};

// Mock data for demonstration
const mockUser = {
  id: 1,
  username: 'CyberAgent',
  wallet_balance: 1250.75,
};

const mockStats = {
  wallet_balance: 1250.75,
  total_accumulated_card_profit: 89.50,
  total_passive_hourly_income: 3.25,
  active_subscriptions: 3,
};

// Utility function for currency formatting
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

// Loading component
const CyberLoader: React.FC = () => (
  <div className="flex items-center justify-center p-8">
    <div className="relative">
      <div className="w-16 h-16 border-4 border-purple-500/30 rounded-full animate-spin">
        <div className="absolute top-0 left-0 w-4 h-4 bg-purple-500 rounded-full animate-pulse"></div>
      </div>
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="w-8 h-8 bg-purple-500/20 rounded-full animate-ping"></div>
      </div>
    </div>
  </div>
);

// Error component
const CyberError: React.FC<{ message: string; onRetry?: () => void }> = ({ message, onRetry }) => (
  <div className="cyber-card p-6 text-center">
    <div className="text-red-400 text-4xl mb-4">⚠️</div>
    <h3 className="font-heading text-lg text-white mb-2">System Error</h3>
    <p className="text-gray-400 mb-4">{message}</p>
    {onRetry && (
      <button onClick={onRetry} className="cyber-button">
        Retry Connection
      </button>
    )}
  </div>
);

// Enhanced stat display with cyberpunk styling
const CyberStatCard: React.FC<{
  label: string;
  value: string;
  icon: React.ReactNode;
  color: 'neon' | 'purple' | 'gold' | 'blue';
  isLoading?: boolean;
  trend?: 'up' | 'down' | 'stable';
}> = ({ label, value, icon, color, isLoading, trend }) => {
  const colorClasses = {
    neon: 'neon-border text-cyan-400',
    purple: 'neon-border-purple text-purple-400',
    gold: 'neon-border-gold text-yellow-400',
    blue: 'border-blue-500/50 text-blue-400',
  };

  const trendIcon = {
    up: '↗️',
    down: '↘️',
    stable: '→',
  };

  return (
    <motion.div
      className={`cyber-card p-4 ${colorClasses[color]} cyber-float`}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
      whileHover={{ scale: 1.02 }}
    >
      <div className="flex items-center justify-between mb-2">
        <div className="text-2xl">{icon}</div>
        {trend && (
          <span className="text-sm opacity-70">{trendIcon[trend]}</span>
        )}
      </div>
      <div className="cyber-stat-label">{label}</div>
      {isLoading ? (
        <div className="h-8 bg-gray-700/50 rounded animate-pulse"></div>
      ) : (
        <div className="cyber-stat-value">{value}</div>
      )}
    </motion.div>
  );
};

// Enhanced live profit card with cyberpunk styling
const CyberProfitCard: React.FC<{
  profit: number;
  onClaim: () => void;
  isClaiming: boolean;
  isLoading?: boolean;
}> = ({ profit, onClaim, isClaiming, isLoading }) => {
  return (
    <motion.div
      className="cyber-card-premium p-6 relative overflow-hidden"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      {/* Animated background effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-yellow-500/10 via-orange-500/10 to-yellow-500/10 animate-pulse"></div>

      <div className="relative z-10">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 rounded-lg bg-yellow-500/20 neon-border-gold">
            <FaCoins className="text-yellow-400 text-xl" />
          </div>
          <div>
            <h3 className="font-display text-lg text-yellow-300 text-glow-gold">PROFIT CORE</h3>
            <p className="text-xs text-gray-400 font-mono">REAL-TIME EARNINGS</p>
          </div>
        </div>

        <div className="text-center mb-6">
          {isLoading ? (
            <div className="h-12 bg-gray-700/50 rounded animate-pulse mb-2"></div>
          ) : (
            <motion.div
              className="text-4xl font-mono font-bold text-yellow-300 text-glow-gold mb-2"
              key={profit}
              initial={{ scale: 1 }}
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 0.4 }}
            >
              {formatCurrency(profit)}
            </motion.div>
          )}
          <div className="flex items-center justify-center gap-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <p className="text-sm text-gray-300 font-mono">READY TO CLAIM</p>
          </div>
        </div>

        <motion.button
          onClick={onClaim}
          disabled={isClaiming || profit <= 0 || isLoading}
          className="cyber-button w-full relative overflow-hidden"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <span className="relative z-10 font-display">
            {isClaiming ? 'PROCESSING...' : 'CLAIM ALL PROFITS'}
          </span>
          {isClaiming && (
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
          )}
        </motion.button>
      </div>
    </motion.div>
  );
};

// Enhanced stats overview with cyberpunk design
const CyberStatsCard: React.FC<{
  subscriptions: number;
  hourlyRate: number;
  isLoading?: boolean;
}> = ({ subscriptions, hourlyRate, isLoading }) => {
  const dailyEarnings = hourlyRate * 24;
  const weeklyEarnings = dailyEarnings * 7;

  return (
    <motion.div
      className="cyber-card-premium p-6 relative"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.2 }}
    >
      <div className="flex items-center gap-3 mb-6">
        <div className="p-2 rounded-lg bg-purple-500/20 neon-border-purple">
          <FaChartLine className="text-purple-400 text-xl" />
        </div>
        <div>
          <h3 className="font-display text-lg text-purple-300 text-glow-purple">ANALYTICS HUB</h3>
          <p className="text-xs text-gray-400 font-mono">PERFORMANCE METRICS</p>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="cyber-bg-glass p-3 rounded-lg text-center">
          <div className="cyber-stat-label mb-1">24H PROJECTION</div>
          {isLoading ? (
            <div className="h-6 bg-gray-700/50 rounded animate-pulse"></div>
          ) : (
            <div className="text-lg font-mono font-bold text-green-400">{formatCurrency(dailyEarnings)}</div>
          )}
        </div>
        <div className="cyber-bg-glass p-3 rounded-lg text-center">
          <div className="cyber-stat-label mb-1">7D PROJECTION</div>
          {isLoading ? (
            <div className="h-6 bg-gray-700/50 rounded animate-pulse"></div>
          ) : (
            <div className="text-lg font-mono font-bold text-blue-400">{formatCurrency(weeklyEarnings)}</div>
          )}
        </div>
      </div>

      <div className="cyber-bg-glass p-4 rounded-lg text-center">
        <div className="cyber-stat-label mb-2">ACTIVE SUBSCRIPTIONS</div>
        {isLoading ? (
          <div className="h-8 bg-gray-700/50 rounded animate-pulse"></div>
        ) : (
          <div className="text-2xl font-mono font-bold text-purple-300 text-glow-purple">{subscriptions}</div>
        )}
        <div className="flex justify-center mt-2">
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
            <span className="text-xs text-gray-400 font-mono">ONLINE</span>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// Enhanced quick actions with cyberpunk gaming design
const CyberQuickActions: React.FC<{
  onTabChange?: (tab: string) => void;
}> = ({ onTabChange }) => {
  const actions = [
    {
      id: 'earn',
      icon: <FaGamepad />,
      label: 'CARD NEXUS',
      description: 'Manage & Earn',
      color: 'neon-border-purple',
      bgColor: 'bg-purple-500/10',
      textColor: 'text-purple-300',
    },
    {
      id: 'premium',
      icon: <FaShieldAlt />,
      label: 'VPN CORE',
      description: 'Premium Access',
      color: 'neon-border',
      bgColor: 'bg-cyan-500/10',
      textColor: 'text-cyan-300',
    },
    {
      id: 'friends',
      icon: <FaUsers />,
      label: 'NETWORK',
      description: 'Invite & Earn',
      color: 'neon-border-gold',
      bgColor: 'bg-yellow-500/10',
      textColor: 'text-yellow-300',
    },
    {
      id: 'wallet',
      icon: <FaWallet />,
      label: 'VAULT',
      description: 'Manage Funds',
      color: 'border-green-500/50',
      bgColor: 'bg-green-500/10',
      textColor: 'text-green-300',
    },
  ];

  return (
    <div className="grid grid-cols-2 gap-4">
      {actions.map((action, index) => (
        <motion.button
          key={action.id}
          onClick={() => onTabChange?.(action.id)}
          className={`cyber-card ${action.bgColor} ${action.color} p-4 text-center group relative overflow-hidden`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
          whileHover={{ scale: 1.05, y: -2 }}
          whileTap={{ scale: 0.95 }}
        >
          {/* Hover effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>

          <div className="relative z-10">
            <div className={`text-3xl mb-3 ${action.textColor} group-hover:scale-110 transition-transform duration-300`}>
              {action.icon}
            </div>
            <div className={`font-display text-sm font-bold ${action.textColor} mb-1 tracking-wider`}>
              {action.label}
            </div>
            <div className="text-xs text-gray-400 font-mono">
              {action.description}
            </div>
          </div>

          {/* Corner accent */}
          <div className={`absolute top-0 right-0 w-0 h-0 border-l-[20px] border-l-transparent border-t-[20px] ${action.textColor.replace('text-', 'border-t-')} opacity-30`}></div>
        </motion.button>
      ))}
    </div>
  );
};

// Internal component with API integration
const HomeTabContentInternal: React.FC<HomeTabContentProps> = ({ onTabChange }) => {
  // API hooks
  const { user, stats, isLoading, isError, error, refetch } = useDashboardData();
  const claimProfitsMutation = useClaimAllProfits();

  // Live profit tracking
  const [liveProfit, setLiveProfit] = useState(0);
  const profitIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Update live profit based on hourly rate
  const hourlyProfit = useMemo(
    () => stats?.total_passive_hourly_income || 0,
    [stats]
  );

  const updateLiveProfit = useCallback(() => {
    const profitIncrement = hourlyProfit / 36000; // Update every 100ms
    if (profitIncrement > 0) {
      setLiveProfit((prev) => prev + profitIncrement);
    }
  }, [hourlyProfit]);

  // Set up live profit updates
  useEffect(() => {
    if (profitIntervalRef.current) clearInterval(profitIntervalRef.current);

    if (hourlyProfit > 0) {
      profitIntervalRef.current = setInterval(updateLiveProfit, 100);
    }

    return () => {
      if (profitIntervalRef.current) clearInterval(profitIntervalRef.current);
    };
  }, [hourlyProfit, updateLiveProfit]);

  // Update base profit when stats change
  useEffect(() => {
    setLiveProfit(stats?.total_accumulated_card_profit || 0);
  }, [stats?.total_accumulated_card_profit]);

  // Handle claim all profits
  const handleClaimAll = useCallback(async () => {
    if (claimProfitsMutation.isLoading || liveProfit <= 0) return;

    try {
      await claimProfitsMutation.mutateAsync();
      setLiveProfit(0);
    } catch (error) {
      console.error('Failed to claim profits:', error);
    }
  }, [claimProfitsMutation, liveProfit]);

  // Handle navigation
  const handleTabChange = useCallback((tab: string) => {
    window.location.href = `/dashboard/${tab}`;
  }, []);

  // Error state
  if (isError) {
    return (
      <div className="flex-1 overflow-y-auto p-4">
        <div className="max-w-4xl mx-auto">
          <CyberError
            message={error?.message || 'Failed to load dashboard data'}
            onRetry={refetch}
          />
        </div>
      </div>
    );
  }

  const currentBalance = stats?.wallet_balance || 0;
  const avatarFilename = user?.telegram_photo_url
    ? `${user.telegram_photo_url}.webp`
    : 'Lovely Angel.webp';

  return (
    <div className="flex-1 overflow-y-auto p-4 cyber-bg">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Enhanced Welcome Section */}
        <motion.div
          className="text-center relative"
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          {/* Background glow effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 via-cyan-500/10 to-purple-500/10 rounded-3xl blur-xl"></div>

          <div className="relative z-10 cyber-card-premium p-8">
            <div className="flex justify-center mb-6">
              <div className="relative">
                <Avatar3DViewer
                  filename={avatarFilename}
                  size={{ width: 100, height: 100 }}
                  className="neon-border-purple cyber-glow"
                />
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-400 rounded-full border-2 border-black animate-pulse"></div>
              </div>
            </div>

            <h1 className="text-gaming-title text-3xl mb-2">
              WELCOME BACK, <span className="text-cyberpunk">{user?.username || 'AGENT'}</span>
            </h1>
            <p className="text-gaming-subtitle mb-4">NEURAL INTERFACE ACTIVE</p>

            {/* Status indicators */}
            <div className="flex justify-center gap-4 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="font-mono text-green-400">ONLINE</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse"></div>
                <span className="font-mono text-cyan-400">SECURE</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                <span className="font-mono text-purple-400">PREMIUM</span>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Balance Display */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <CyberStatCard
            label="WALLET BALANCE"
            value={formatCurrency(currentBalance)}
            icon={<FaWallet />}
            color="purple"
            isLoading={isLoading}
            trend="up"
          />
        </motion.div>

        {/* Main Stats Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <CyberProfitCard
            profit={liveProfit}
            onClaim={handleClaimAll}
            isClaiming={claimProfitsMutation.isLoading}
            isLoading={isLoading}
          />
          <CyberStatsCard
            subscriptions={stats?.active_subscriptions || 0}
            hourlyRate={hourlyProfit}
            isLoading={isLoading}
          />
        </div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <div className="text-center mb-6">
            <h2 className="font-display text-2xl text-white text-glow mb-2">COMMAND CENTER</h2>
            <p className="text-gaming-subtitle">ACCESS CORE SYSTEMS</p>
          </div>
          <CyberQuickActions onTabChange={handleTabChange} />
        </motion.div>

        {/* Loading overlay */}
        <AnimatePresence>
          {isLoading && (
            <motion.div
              className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <CyberLoader />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

// Main component with QueryProvider wrapper
const HomeTabContent: React.FC<HomeTabContentProps> = (props) => {
  return (
    <QueryProvider>
      <HomeTabContentInternal {...props} />
    </QueryProvider>
  );
};

export default HomeTabContent;
