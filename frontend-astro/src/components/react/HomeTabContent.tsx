/**
 * @file HomeTabContent.tsx
 * @description Home tab content component - migrated from React HomeTab
 * Interactive React island for dashboard home with real-time features
 */

import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { motion } from 'framer-motion';
import { FaCoins, FaBolt, FaUsers, FaShield } from 'react-icons/fa';
import Avatar3DViewer from './Avatar3DViewer';

// Types
interface User {
  id: number;
  username: string;
  email?: string;
  telegram_photo_url?: string;
  wallet_balance: number;
}

interface DashboardStats {
  wallet_balance: number;
  total_accumulated_card_profit: number;
  total_passive_hourly_income: number;
  active_subscriptions: number;
}

interface HomeTabContentProps {
  user?: User;
  dashboardStats?: DashboardStats;
  onTabChange?: (tab: string) => void;
}

// Utility function for currency formatting
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

// Cyberpunk-style stat display component
const CyberpunkStatDisplay: React.FC<{
  label: string;
  value: string;
  icon: React.ReactNode;
  color: 'gold' | 'purple' | 'blue';
}> = ({ label, value, icon, color }) => {
  const colorClasses = {
    gold: 'text-yellow-400 border-yellow-500/30 bg-yellow-500/10',
    purple: 'text-purple-400 border-purple-500/30 bg-purple-500/10',
    blue: 'text-blue-400 border-blue-500/30 bg-blue-500/10',
  };

  return (
    <div className={`relative p-4 rounded-xl border backdrop-blur-sm ${colorClasses[color]}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="text-2xl">{icon}</div>
          <div>
            <div className="text-sm font-medium text-white/70">{label}</div>
            <div className="text-xl font-bold text-white">{value}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Live profit card with claim functionality
const LiveProfitCard: React.FC<{
  profit: number;
  onClaim: () => void;
  isClaiming: boolean;
}> = ({ profit, onClaim, isClaiming }) => {
  return (
    <motion.div
      className="bg-gradient-to-br from-yellow-500/20 to-orange-600/20 backdrop-blur-sm rounded-xl p-6 border border-yellow-500/30"
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="flex items-center gap-2 mb-4">
        <FaCoins className="text-yellow-400 text-lg" />
        <h3 className="text-lg font-semibold text-yellow-300">Earnings Hub</h3>
      </div>

      <div className="text-center">
        <motion.div
          className="text-3xl font-bold text-white mb-2"
          key={profit}
          initial={{ scale: 1 }}
          animate={{ scale: [1, 1.05, 1] }}
          transition={{ duration: 0.3 }}
        >
          {formatCurrency(profit)}
        </motion.div>
        <p className="text-yellow-200/70 text-sm mb-4">Available to claim</p>

        <button
          onClick={onClaim}
          disabled={isClaiming || profit <= 0}
          className="w-full bg-yellow-500 hover:bg-yellow-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-black font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
        >
          {isClaiming ? 'Claiming...' : 'Claim All'}
        </button>
      </div>
    </motion.div>
  );
};

// Stats overview card
const StatsCard: React.FC<{
  subscriptions: number;
  hourlyRate: number;
}> = ({ subscriptions, hourlyRate }) => {
  const dailyEarnings = hourlyRate * 24;
  const weeklyEarnings = dailyEarnings * 7;

  return (
    <motion.div
      className="bg-gradient-to-br from-purple-500/20 to-blue-600/20 backdrop-blur-sm rounded-xl p-6 border border-purple-500/30"
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5, delay: 0.1 }}
    >
      <div className="flex items-center gap-2 mb-4">
        <FaBolt className="text-purple-400 text-lg" />
        <h3 className="text-lg font-semibold text-purple-300">Income Projections</h3>
      </div>

      <div className="grid grid-cols-2 gap-3">
        <div className="bg-white/5 rounded-lg p-3 text-center">
          <div className="text-sm text-white/60">Daily</div>
          <div className="text-lg font-bold text-white">{formatCurrency(dailyEarnings)}</div>
        </div>
        <div className="bg-white/5 rounded-lg p-3 text-center">
          <div className="text-sm text-white/60">Weekly</div>
          <div className="text-lg font-bold text-white">{formatCurrency(weeklyEarnings)}</div>
        </div>
        <div className="bg-white/5 rounded-lg p-3 text-center col-span-2">
          <div className="text-sm text-white/60">Active Subscriptions</div>
          <div className="text-xl font-bold text-purple-300">{subscriptions}</div>
        </div>
      </div>
    </motion.div>
  );
};

// Quick action buttons
const QuickActions: React.FC<{
  onTabChange?: (tab: string) => void;
}> = ({ onTabChange }) => {
  const actions = [
    {
      id: 'earn',
      icon: <FaCoins />,
      label: 'Manage Cards',
      description: 'Earn rewards',
      color: 'from-purple-500/20 to-purple-600/20 border-purple-500/30',
    },
    {
      id: 'premium',
      icon: <FaShield />,
      label: 'VPN Services',
      description: 'Premium access',
      color: 'from-blue-500/20 to-blue-600/20 border-blue-500/30',
    },
    {
      id: 'friends',
      icon: <FaUsers />,
      label: 'Invite Friends',
      description: 'Get bonuses',
      color: 'from-green-500/20 to-green-600/20 border-green-500/30',
    },
    {
      id: 'wallet',
      icon: <FaBolt />,
      label: 'Wallet',
      description: 'Manage funds',
      color: 'from-yellow-500/20 to-yellow-600/20 border-yellow-500/30',
    },
  ];

  return (
    <div className="grid grid-cols-2 gap-3">
      {actions.map((action, index) => (
        <motion.button
          key={action.id}
          onClick={() => onTabChange?.(action.id)}
          className={`bg-gradient-to-br ${action.color} backdrop-blur-sm rounded-xl p-4 text-center border transition-all duration-300 hover:scale-105`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <div className="text-2xl mb-2 text-white">{action.icon}</div>
          <div className="text-sm font-semibold text-white">{action.label}</div>
          <div className="text-xs text-white/60">{action.description}</div>
        </motion.button>
      ))}
    </div>
  );
};

// Main HomeTabContent component
const HomeTabContent: React.FC<HomeTabContentProps> = ({
  user,
  dashboardStats,
  onTabChange,
}) => {
  // Live profit tracking
  const [liveProfit, setLiveProfit] = useState(dashboardStats?.total_accumulated_card_profit || 0);
  const [isClaiming, setIsClaiming] = useState(false);
  const profitIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Update live profit based on hourly rate
  const hourlyProfit = useMemo(
    () => dashboardStats?.total_passive_hourly_income || 0,
    [dashboardStats]
  );

  const updateLiveProfit = useCallback(() => {
    const profitIncrement = hourlyProfit / 36000; // Update every 100ms
    if (profitIncrement > 0) {
      setLiveProfit((prev) => prev + profitIncrement);
    }
  }, [hourlyProfit]);

  // Set up live profit updates
  useEffect(() => {
    if (profitIntervalRef.current) clearInterval(profitIntervalRef.current);

    if (hourlyProfit > 0) {
      profitIntervalRef.current = setInterval(updateLiveProfit, 100);
    }

    return () => {
      if (profitIntervalRef.current) clearInterval(profitIntervalRef.current);
    };
  }, [hourlyProfit, updateLiveProfit]);

  // Update base profit when stats change
  useEffect(() => {
    setLiveProfit(dashboardStats?.total_accumulated_card_profit || 0);
  }, [dashboardStats?.total_accumulated_card_profit]);

  // Handle claim all profits
  const handleClaimAll = useCallback(async () => {
    if (isClaiming || liveProfit <= 0) return;

    setIsClaiming(true);
    try {
      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      setLiveProfit(0);
      // TODO: Update user balance
    } catch (error) {
      console.error('Failed to claim profits:', error);
    } finally {
      setIsClaiming(false);
    }
  }, [isClaiming, liveProfit]);

  // Handle navigation
  const handleTabChange = useCallback((tab: string) => {
    window.location.href = `/dashboard/${tab}`;
  }, []);

  const currentBalance = dashboardStats?.wallet_balance || 0;
  const avatarFilename = user?.telegram_photo_url
    ? `${user.telegram_photo_url}.webp`
    : 'Lovely Angel.webp';

  return (
    <div className="flex-1 overflow-y-auto p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Welcome Section */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex justify-center mb-4">
            <Avatar3DViewer
              filename={avatarFilename}
              size={{ width: 80, height: 80 }}
              className="ring-4 ring-purple-500/30"
            />
          </div>
          <h1 className="text-2xl font-bold text-white mb-2">
            Welcome back, <span className="text-purple-300">{user?.username || 'User'}</span>!
          </h1>
          <p className="text-white/70">Your digital command center</p>
        </motion.div>

        {/* Balance Display */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <CyberpunkStatDisplay
            label="Current Balance"
            value={formatCurrency(currentBalance)}
            icon={<FaCoins />}
            color="purple"
          />
        </motion.div>

        {/* Main Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <LiveProfitCard
            profit={liveProfit}
            onClaim={handleClaimAll}
            isClaiming={isClaiming}
          />
          <StatsCard
            subscriptions={dashboardStats?.active_subscriptions || 0}
            hourlyRate={hourlyProfit}
          />
        </div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <h2 className="text-xl font-semibold text-white mb-4">Quick Access</h2>
          <QuickActions onTabChange={handleTabChange} />
        </motion.div>
      </div>
    </div>
  );
};

export default HomeTabContent;
