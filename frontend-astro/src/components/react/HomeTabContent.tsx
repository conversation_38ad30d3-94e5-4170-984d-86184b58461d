/**
 * @file HomeTabContentNew.tsx
 * @description Enhanced cyberpunk gaming dashboard home
 * Complete React component with full functionality
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaCoins, FaBolt, FaUsers, FaWallet, FaGamepad, FaChartLine, FaShieldAlt } from 'react-icons/fa';
import { api, handleAPIError, type User, type UserStats } from '../../services/api';
import { telegramWebApp } from '../../utils/telegramWebApp';

// Types
interface HomeTabContentProps {
  onTabChange?: (tab: string) => void;
}

// Currency formatter
const formatCurrency = (amount: number): string => {
  return `$${amount.toFixed(2)}`;
};

// Compact Cyberpunk Stat Card Component
const CyberStatCard: React.FC<{
  label: string;
  value: string;
  icon: React.ReactNode;
  color: 'neon' | 'purple' | 'gold' | 'blue';
  trend?: 'up' | 'down' | 'stable';
}> = ({ label, value, icon, color, trend }) => {
  const colorClasses = {
    neon: 'border-cyan-400/50 text-cyan-400 bg-cyan-500/10',
    purple: 'border-purple-400/50 text-purple-400 bg-purple-500/10',
    gold: 'border-yellow-400/50 text-yellow-400 bg-yellow-500/10',
    blue: 'border-blue-400/50 text-blue-400 bg-blue-500/10',
  };

  const trendIcon = {
    up: '↗️',
    down: '↘️',
    stable: '→',
  };

  return (
    <motion.div
      className={`p-3 rounded-lg border backdrop-blur-sm ${colorClasses[color]} relative overflow-hidden`}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      whileHover={{ scale: 1.01 }}
    >
      <div className="flex items-center justify-between mb-1">
        <div className="text-lg">{icon}</div>
        {trend && (
          <span className="text-xs opacity-70">{trendIcon[trend]}</span>
        )}
      </div>
      <div className="text-xs font-medium text-white/70 uppercase tracking-wide">{label}</div>
      <div className="text-lg font-bold text-white">{value}</div>
    </motion.div>
  );
};

// Compact Live Profit Card Component
const LiveProfitCard: React.FC<{
  profit: number;
  onClaim: () => void;
  isClaiming: boolean;
}> = ({ profit, onClaim, isClaiming }) => {
  return (
    <motion.div
      className="bg-gradient-to-br from-yellow-500/20 to-orange-600/20 backdrop-blur-sm rounded-lg p-4 border border-yellow-500/30 relative overflow-hidden"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Animated background effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-yellow-500/10 via-orange-500/10 to-yellow-500/10 animate-pulse"></div>

      <div className="relative z-10">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <div className="p-1.5 rounded-lg bg-yellow-500/20 border border-yellow-500/50">
              <FaCoins className="text-yellow-400 text-sm" />
            </div>
            <div>
              <h3 className="text-sm font-bold text-yellow-300 uppercase tracking-wide">PROFIT CORE</h3>
              <p className="text-xs text-gray-400 font-mono">REAL-TIME</p>
            </div>
          </div>
          <div className="text-right">
            <motion.div
              className="text-xl font-bold text-yellow-300"
              key={profit}
              initial={{ scale: 1 }}
              animate={{ scale: [1, 1.05, 1] }}
              transition={{ duration: 0.3 }}
            >
              {formatCurrency(profit)}
            </motion.div>
            <div className="flex items-center justify-end gap-1">
              <div className="w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse"></div>
              <p className="text-xs text-gray-300 font-mono">READY</p>
            </div>
          </div>
        </div>

        <motion.button
          onClick={onClaim}
          disabled={isClaiming || profit <= 0}
          className="w-full bg-yellow-500 hover:bg-yellow-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-black font-bold py-2 px-4 rounded-lg transition-colors text-sm uppercase tracking-wide"
          whileHover={{ scale: 1.01 }}
          whileTap={{ scale: 0.99 }}
        >
          {isClaiming ? 'PROCESSING...' : 'CLAIM PROFITS'}
        </motion.button>
      </div>
    </motion.div>
  );
};

// Compact Stats Overview Card
const StatsCard: React.FC<{
  subscriptions: number;
  hourlyRate: number;
}> = ({ subscriptions, hourlyRate }) => {
  const dailyEarnings = hourlyRate * 24;

  return (
    <motion.div
      className="bg-gradient-to-br from-purple-500/20 to-blue-600/20 backdrop-blur-sm rounded-lg p-4 border border-purple-500/30 relative"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.1 }}
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <div className="p-1.5 rounded-lg bg-purple-500/20 border border-purple-500/50">
            <FaChartLine className="text-purple-400 text-sm" />
          </div>
          <div>
            <h3 className="text-sm font-bold text-purple-300 uppercase tracking-wide">ANALYTICS</h3>
            <p className="text-xs text-gray-400 font-mono">METRICS</p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-lg font-bold text-purple-300">{subscriptions}</div>
          <div className="flex items-center justify-end gap-1">
            <div className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-pulse"></div>
            <span className="text-xs text-gray-400 font-mono">ACTIVE</span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-2">
        <div className="bg-black/30 p-2 rounded-lg text-center">
          <div className="text-xs text-gray-400 uppercase tracking-wide mb-1">24H</div>
          <div className="text-sm font-bold text-green-400">{formatCurrency(dailyEarnings)}</div>
        </div>
        <div className="bg-black/30 p-2 rounded-lg text-center">
          <div className="text-xs text-gray-400 uppercase tracking-wide mb-1">HOURLY</div>
          <div className="text-sm font-bold text-blue-400">{formatCurrency(hourlyRate)}</div>
        </div>
      </div>
    </motion.div>
  );
};

// Quick Actions Component
const QuickActions: React.FC<{
  onTabChange?: (tab: string) => void;
}> = ({ onTabChange }) => {
  const actions = [
    {
      id: 'earn',
      icon: <FaGamepad />,
      label: 'CARD NEXUS',
      description: 'Manage & Earn',
      color: 'bg-purple-500/20 border-purple-500/30 hover:bg-purple-500/30',
      textColor: 'text-purple-300',
    },
    {
      id: 'premium',
      icon: <FaShieldAlt />,
      label: 'VPN CORE',
      description: 'Premium Access',
      color: 'bg-cyan-500/20 border-cyan-500/30 hover:bg-cyan-500/30',
      textColor: 'text-cyan-300',
    },
    {
      id: 'friends',
      icon: <FaUsers />,
      label: 'NETWORK',
      description: 'Invite & Earn',
      color: 'bg-yellow-500/20 border-yellow-500/30 hover:bg-yellow-500/30',
      textColor: 'text-yellow-300',
    },
    {
      id: 'wallet',
      icon: <FaWallet />,
      label: 'VAULT',
      description: 'Manage Funds',
      color: 'bg-green-500/20 border-green-500/30 hover:bg-green-500/30',
      textColor: 'text-green-300',
    },
  ];

  return (
    <div className="grid grid-cols-2 gap-3">
      {actions.map((action, index) => (
        <motion.button
          key={action.id}
          onClick={() => onTabChange?.(action.id)}
          className={`${action.color} backdrop-blur-sm rounded-lg p-3 text-center border transition-all duration-200 relative overflow-hidden group`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 + index * 0.05 }}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          {/* Hover effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-500"></div>

          <div className="relative z-10">
            <div className={`text-xl mb-2 ${action.textColor} group-hover:scale-105 transition-transform duration-200`}>
              {action.icon}
            </div>
            <div className={`text-xs font-bold ${action.textColor} mb-1 tracking-wider uppercase`}>
              {action.label}
            </div>
            <div className="text-xs text-gray-400 font-mono">
              {action.description}
            </div>
          </div>

          {/* Corner accent */}
          <div className={`absolute top-0 right-0 w-0 h-0 border-l-[12px] border-l-transparent border-t-[12px] ${action.textColor.replace('text-', 'border-t-')} opacity-30`}></div>
        </motion.button>
      ))}
    </div>
  );
};

// Main HomeTabContent Component
const HomeTabContent: React.FC<HomeTabContentProps> = ({ onTabChange }) => {
  // State management
  const [user, setUser] = useState<User | null>(null);
  const [stats, setStats] = useState<UserStats | null>(null);
  const [liveProfit, setLiveProfit] = useState(0);
  const [isClaiming, setIsClaiming] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const profitIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Get hourly profit from stats
  const hourlyProfit = stats?.total_passive_hourly_income || 0;

  const updateLiveProfit = useCallback(() => {
    const profitIncrement = hourlyProfit / 36000; // Update every 100ms
    if (profitIncrement > 0) {
      setLiveProfit((prev) => prev + profitIncrement);
    }
  }, [hourlyProfit]);

  // Set up live profit updates
  useEffect(() => {
    if (profitIntervalRef.current) clearInterval(profitIntervalRef.current);

    if (hourlyProfit > 0) {
      profitIntervalRef.current = setInterval(updateLiveProfit, 100);
    }

    return () => {
      if (profitIntervalRef.current) clearInterval(profitIntervalRef.current);
    };
  }, [hourlyProfit, updateLiveProfit]);

  // Handle claim all profits
  const handleClaimAll = useCallback(async () => {
    if (isClaiming || liveProfit <= 0) return;

    setIsClaiming(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      setLiveProfit(0);
    } catch (error) {
      console.error('Failed to claim profits:', error);
    } finally {
      setIsClaiming(false);
    }
  }, [isClaiming, liveProfit]);

  // Handle navigation
  const handleTabChange = useCallback((tab: string) => {
    window.location.href = `/dashboard/${tab}`;
  }, []);

  const currentBalance = mockStats.wallet_balance;

  return (
    <div className="flex-1 overflow-y-auto p-3 bg-black">
      <div className="max-w-md mx-auto space-y-4">
        {/* Compact Welcome Section */}
        <motion.div
          className="text-center relative"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
        >
          <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-4 border border-purple-500/30">
            <div className="flex items-center justify-center gap-3 mb-3">
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-cyan-500 rounded-full flex items-center justify-center">
                  <FaGamepad className="text-lg text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border border-black animate-pulse"></div>
              </div>
              <div className="text-left">
                <h1 className="text-lg font-bold text-white">
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-cyan-400">{mockUser.username}</span>
                </h1>
                <p className="text-xs text-gray-400 font-mono">NEURAL INTERFACE</p>
              </div>
            </div>

            {/* Compact Status indicators */}
            <div className="flex justify-center gap-3 text-xs">
              <div className="flex items-center gap-1">
                <div className="w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse"></div>
                <span className="font-mono text-green-400">ONLINE</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-1.5 h-1.5 bg-cyan-400 rounded-full animate-pulse"></div>
                <span className="font-mono text-cyan-400">SECURE</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-pulse"></div>
                <span className="font-mono text-purple-400">PREMIUM</span>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Compact Balance Display */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <CyberStatCard
            label="WALLET BALANCE"
            value={formatCurrency(currentBalance)}
            icon={<FaWallet />}
            color="purple"
            trend="up"
          />
        </motion.div>

        {/* Compact Main Cards */}
        <div className="space-y-4">
          <LiveProfitCard
            profit={liveProfit}
            onClaim={handleClaimAll}
            isClaiming={isClaiming}
          />
          <StatsCard
            subscriptions={mockStats.active_subscriptions}
            hourlyRate={hourlyProfit}
          />
        </div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <div className="text-center mb-3">
            <h2 className="text-lg font-bold text-white uppercase tracking-wide mb-1">COMMAND CENTER</h2>
            <p className="text-xs text-gray-400 uppercase tracking-wide">ACCESS SYSTEMS</p>
          </div>
          <QuickActions onTabChange={handleTabChange} />
        </motion.div>

        {/* Loading overlay */}
        <AnimatePresence>
          {isClaiming && (
            <motion.div
              className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <div className="flex items-center justify-center p-8">
                <div className="relative">
                  <div className="w-16 h-16 border-4 border-purple-500/30 rounded-full animate-spin">
                    <div className="absolute top-0 left-0 w-4 h-4 bg-purple-500 rounded-full animate-pulse"></div>
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-8 h-8 bg-purple-500/20 rounded-full animate-ping"></div>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default HomeTabContent;
