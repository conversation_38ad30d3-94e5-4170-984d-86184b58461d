/**
 * @file EarnTabContent.tsx
 * @description Enhanced cyberpunk earn tab with card management and task system
 */

import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaCoins, FaGamepad, FaTasks, FaChessRook, FaCheckCircle, FaSpinner } from 'react-icons/fa';

// Types
interface EarnTabContentProps {
  onTabChange?: (tab: string) => void;
}

// Mock data
const mockCards = [
  { id: 1, name: 'Cyber Miner', level: 3, profit_per_hour: 2.5, upgrade_cost: 150, category: 'Mining', is_owned: true },
  { id: 2, name: 'Data Harvester', level: 1, profit_per_hour: 1.2, upgrade_cost: 75, category: 'Data', is_owned: true },
  { id: 3, name: 'Neural Network', level: 0, profit_per_hour: 5.0, upgrade_cost: 500, category: 'AI', is_owned: false },
];

const mockTasks = [
  { id: 1, title: 'Daily Login', description: 'Login to the platform daily', reward: 10, status: 'completed', type: 'daily' },
  { id: 2, title: 'Invite 3 Friends', description: 'Invite 3 friends to join VIPVerse', reward: 50, status: 'available', type: 'social' },
  { id: 3, title: 'Complete 5 Tasks', description: 'Complete 5 different tasks', reward: 25, status: 'in_progress', type: 'achievement' },
];

const formatCurrency = (amount: number): string => `$${amount.toFixed(2)}`;

// Segmented Control
const SegmentedControl: React.FC<{
  options: Array<{ id: string; label: string; icon: React.ComponentType }>;
  activeOption: string;
  onChange: (option: string) => void;
}> = ({ options, activeOption, onChange }) => (
  <div className="flex bg-gray-900/50 backdrop-blur-sm rounded-xl p-1 border border-gray-700/50">
    {options.map((option) => {
      const Icon = option.icon;
      return (
        <button
          key={option.id}
          onClick={() => onChange(option.id)}
          className={`flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-lg transition-all duration-300 ${
            activeOption === option.id
              ? 'bg-purple-500/30 text-purple-300 border border-purple-500/50'
              : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
          }`}
        >
          <Icon />
          <span className="font-medium uppercase tracking-wide">{option.label}</span>
        </button>
      );
    })}
  </div>
);

// Card Component
const CardItem: React.FC<{
  card: any;
  onUpgrade: (cardId: number) => void;
  onBuy: (cardId: number) => void;
  isProcessing: boolean;
}> = ({ card, onUpgrade, onBuy, isProcessing }) => (
  <motion.div
    className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-lg p-3 border border-gray-700/50 relative overflow-hidden group"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.3 }}
    whileHover={{ scale: 1.01 }}
  >
    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-purple-500/5 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-500"></div>

    <div className="relative z-10">
      <div className="flex items-center gap-2 mb-3">
        <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-cyan-500 rounded-lg flex items-center justify-center">
          <FaChessRook className="text-white text-sm" />
        </div>
        <div className="flex-1">
          <h3 className="text-sm font-bold text-white uppercase tracking-wide">{card.name}</h3>
          <p className="text-xs text-gray-400 font-mono">{card.category}</p>
        </div>
        {card.is_owned && <div className="text-green-400 text-sm"><FaCheckCircle /></div>}
      </div>

      <div className="grid grid-cols-2 gap-2 mb-3">
        <div className="bg-black/30 p-2 rounded-lg text-center">
          <div className="text-xs text-gray-400 uppercase">Level</div>
          <div className="text-sm font-bold text-purple-300">{card.level}</div>
        </div>
        <div className="bg-black/30 p-2 rounded-lg text-center">
          <div className="text-xs text-gray-400 uppercase">Per Hour</div>
          <div className="text-sm font-bold text-yellow-300">{formatCurrency(card.profit_per_hour)}</div>
        </div>
      </div>

      <button
        onClick={() => card.is_owned ? onUpgrade(card.id) : onBuy(card.id)}
        disabled={isProcessing}
        className={`w-full py-2 px-3 rounded-lg font-bold text-xs uppercase tracking-wide transition-colors ${
          card.is_owned
            ? 'bg-purple-500 hover:bg-purple-600 text-white'
            : 'bg-cyan-500 hover:bg-cyan-600 text-black'
        } disabled:opacity-50 disabled:cursor-not-allowed`}
      >
        {isProcessing ? (
          <div className="flex items-center justify-center gap-1">
            <FaSpinner className="animate-spin text-xs" />
            <span>Processing...</span>
          </div>
        ) : (
          <span>
            {card.is_owned ? `Upgrade ${formatCurrency(card.upgrade_cost)}` : `Buy ${formatCurrency(card.upgrade_cost)}`}
          </span>
        )}
      </button>
    </div>
  </motion.div>
);

// Task Component
const TaskItem: React.FC<{
  task: any;
  onStart: (taskId: number) => void;
  onClaim: (taskId: number) => void;
  isProcessing: boolean;
}> = ({ task, onStart, onClaim, isProcessing }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-400 border-green-500/30 bg-green-500/10';
      case 'in_progress': return 'text-yellow-400 border-yellow-500/30 bg-yellow-500/10';
      default: return 'text-blue-400 border-blue-500/30 bg-blue-500/10';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'Claim Reward';
      case 'in_progress': return 'In Progress';
      default: return 'Start Task';
    }
  };

  return (
    <motion.div
      className={`p-4 rounded-xl border backdrop-blur-sm ${getStatusColor(task.status)} relative overflow-hidden group`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      whileHover={{ scale: 1.02 }}
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-3">
          <FaTasks className="text-xl" />
          <div>
            <h3 className="font-bold text-white uppercase tracking-wide">{task.title}</h3>
            <p className="text-xs text-gray-400">{task.description}</p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-lg font-bold text-yellow-300">{formatCurrency(task.reward)}</div>
          <div className="text-xs text-gray-400 uppercase">{task.type}</div>
        </div>
      </div>
      
      <button
        onClick={() => task.status === 'completed' ? onClaim(task.id) : onStart(task.id)}
        disabled={isProcessing || task.status === 'in_progress'}
        className="w-full py-2 px-4 bg-white/10 hover:bg-white/20 disabled:bg-gray-600/50 disabled:cursor-not-allowed text-white font-bold rounded-lg transition-colors uppercase tracking-wide"
      >
        {isProcessing ? (
          <div className="flex items-center justify-center gap-2">
            <FaSpinner className="animate-spin" />
            <span>Processing...</span>
          </div>
        ) : (
          getStatusText(task.status)
        )}
      </button>
    </motion.div>
  );
};

// Main Component
const EarnTabContent: React.FC<EarnTabContentProps> = ({ onTabChange }) => {
  const [activeSection, setActiveSection] = useState<'cards' | 'tasks' | 'games'>('cards');
  const [processingId, setProcessingId] = useState<number | null>(null);

  const segmentedOptions = [
    { id: 'cards', label: 'Cards', icon: FaChessRook },
    { id: 'tasks', label: 'Tasks', icon: FaTasks },
    { id: 'games', label: 'Games', icon: FaGamepad },
  ];

  const handleCardUpgrade = useCallback(async (cardId: number) => {
    setProcessingId(cardId);
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      console.log('Card upgraded:', cardId);
    } catch (error) {
      console.error('Failed to upgrade card:', error);
    } finally {
      setProcessingId(null);
    }
  }, []);

  const handleCardBuy = useCallback(async (cardId: number) => {
    setProcessingId(cardId);
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      console.log('Card purchased:', cardId);
    } catch (error) {
      console.error('Failed to buy card:', error);
    } finally {
      setProcessingId(null);
    }
  }, []);

  const handleTaskStart = useCallback(async (taskId: number) => {
    setProcessingId(taskId);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Task started:', taskId);
    } catch (error) {
      console.error('Failed to start task:', error);
    } finally {
      setProcessingId(null);
    }
  }, []);

  const handleTaskClaim = useCallback(async (taskId: number) => {
    setProcessingId(taskId);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Task reward claimed:', taskId);
    } catch (error) {
      console.error('Failed to claim task reward:', error);
    } finally {
      setProcessingId(null);
    }
  }, []);

  return (
    <div className="flex-1 overflow-y-auto p-3 bg-black">
      <div className="max-w-md mx-auto space-y-4">
        {/* Header */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <h1 className="text-xl font-bold text-white mb-1 uppercase tracking-wide">
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-cyan-400">EARN CENTER</span>
          </h1>
          <p className="text-xs text-gray-400 uppercase tracking-wide">MAXIMIZE PROFITS</p>
        </motion.div>

        {/* Segmented Control */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <SegmentedControl
            options={segmentedOptions}
            activeOption={activeSection}
            onChange={(option) => setActiveSection(option as 'cards' | 'tasks' | 'games')}
          />
        </motion.div>

        {/* Content */}
        <AnimatePresence mode="wait">
          {activeSection === 'cards' && (
            <motion.div
              key="cards"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.5 }}
              className="space-y-4"
            >
              <h2 className="text-lg font-bold text-white uppercase tracking-wide">Card Collection</h2>
              <div className="grid grid-cols-1 gap-3">
                {mockCards.map((card, index) => (
                  <motion.div
                    key={card.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  >
                    <CardItem
                      card={card}
                      onUpgrade={handleCardUpgrade}
                      onBuy={handleCardBuy}
                      isProcessing={processingId === card.id}
                    />
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {activeSection === 'tasks' && (
            <motion.div
              key="tasks"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.5 }}
              className="space-y-4"
            >
              <h2 className="text-xl font-bold text-white uppercase tracking-wide">Available Tasks</h2>
              <div className="space-y-4">
                {mockTasks.map((task, index) => (
                  <motion.div
                    key={task.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  >
                    <TaskItem
                      task={task}
                      onStart={handleTaskStart}
                      onClaim={handleTaskClaim}
                      isProcessing={processingId === task.id}
                    />
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {activeSection === 'games' && (
            <motion.div
              key="games"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.5 }}
              className="text-center py-16"
            >
              <FaGamepad className="text-6xl text-purple-400 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-white mb-2 uppercase tracking-wide">Games Coming Soon</h2>
              <p className="text-gray-400">Interactive games and challenges will be available here</p>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default EarnTabContent;
