/**
 * @file FriendsTabContentNew.tsx
 * @description Enhanced cyberpunk friends tab with referral system
 */

import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { FaUsers, FaShare, FaCopy, FaUserPlus, FaGift, FaCheck } from 'react-icons/fa';

// Types
interface FriendsTabContentProps {
  onTabChange?: (tab: string) => void;
}

// Mock data
const mockReferralStats = {
  total_referred: 12,
  total_earned: 245.50,
  pending_rewards: 35.00,
  referral_code: 'CYBER2024',
  referral_link: 'https://vipverse.com/ref/CYBER2024',
};

const mockReferredUsers = [
  { id: 1, username: 'Agent001', joined_at: '2024-01-15', earned_from: 25.00, status: 'active' },
  { id: 2, username: '<PERSON><PERSON><PERSON><PERSON><PERSON>', joined_at: '2024-01-20', earned_from: 45.50, status: 'active' },
  { id: 3, username: '<PERSON><PERSON><PERSON>', joined_at: '2024-01-25', earned_from: 15.00, status: 'pending' },
];

const formatCurrency = (amount: number): string => `$${amount.toFixed(2)}`;

// Main Component
const FriendsTabContent: React.FC<FriendsTabContentProps> = ({ onTabChange }) => {
  const [copiedText, setCopiedText] = useState<string | null>(null);

  const handleCopy = useCallback(async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(text);
      setTimeout(() => setCopiedText(null), 2000);
    } catch (error) {
      console.error('Failed to copy text:', error);
    }
  }, []);

  return (
    <div className="flex-1 overflow-y-auto p-4 bg-black">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-3xl font-bold text-white mb-2 uppercase tracking-wide">
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-cyan-400">FRIENDS NETWORK</span>
          </h1>
          <p className="text-gray-400 uppercase tracking-wide">BUILD YOUR REFERRAL EMPIRE</p>
        </motion.div>

        {/* Referral Stats */}
        <motion.div
          className="bg-gradient-to-br from-purple-500/20 to-blue-600/20 backdrop-blur-sm rounded-xl p-6 border border-purple-500/30"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 rounded-lg bg-purple-500/20 border border-purple-500/50">
              <FaUsers className="text-purple-400 text-xl" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-purple-300 uppercase tracking-wide">REFERRAL STATS</h3>
              <p className="text-xs text-gray-400 font-mono">YOUR NETWORK PERFORMANCE</p>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-black/30 p-3 rounded-lg text-center">
              <div className="text-sm text-gray-400 uppercase tracking-wide mb-1">Total Referred</div>
              <div className="text-2xl font-bold text-purple-300">{mockReferralStats.total_referred}</div>
            </div>
            <div className="bg-black/30 p-3 rounded-lg text-center">
              <div className="text-sm text-gray-400 uppercase tracking-wide mb-1">Total Earned</div>
              <div className="text-2xl font-bold text-yellow-300">{formatCurrency(mockReferralStats.total_earned)}</div>
            </div>
            <div className="bg-black/30 p-3 rounded-lg text-center col-span-2">
              <div className="text-sm text-gray-400 uppercase tracking-wide mb-1">Pending Rewards</div>
              <div className="text-xl font-bold text-green-300">{formatCurrency(mockReferralStats.pending_rewards)}</div>
            </div>
          </div>
        </motion.div>

        {/* Share Options and Referred Users */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Share Options */}
          <motion.div
            className="bg-gradient-to-br from-cyan-500/20 to-teal-600/20 backdrop-blur-sm rounded-xl p-6 border border-cyan-500/30"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 rounded-lg bg-cyan-500/20 border border-cyan-500/50">
                <FaShare className="text-cyan-400 text-xl" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-cyan-300 uppercase tracking-wide">SHARE & EARN</h3>
                <p className="text-xs text-gray-400 font-mono">INVITE FRIENDS TO JOIN</p>
              </div>
            </div>
            
            <div className="space-y-4">
              {/* Referral Code */}
              <div>
                <label className="text-sm text-gray-400 uppercase tracking-wide mb-2 block">Referral Code</label>
                <div className="flex gap-2">
                  <div className="flex-1 bg-black/30 p-3 rounded-lg font-mono text-cyan-300 text-center text-lg font-bold">
                    {mockReferralStats.referral_code}
                  </div>
                  <button
                    onClick={() => handleCopy(mockReferralStats.referral_code)}
                    className="px-4 py-3 bg-cyan-500 hover:bg-cyan-600 text-black font-bold rounded-lg transition-colors"
                  >
                    {copiedText === mockReferralStats.referral_code ? <FaCheck /> : <FaCopy />}
                  </button>
                </div>
              </div>
              
              {/* Referral Link */}
              <div>
                <label className="text-sm text-gray-400 uppercase tracking-wide mb-2 block">Referral Link</label>
                <div className="flex gap-2">
                  <div className="flex-1 bg-black/30 p-3 rounded-lg font-mono text-cyan-300 text-sm truncate">
                    {mockReferralStats.referral_link}
                  </div>
                  <button
                    onClick={() => handleCopy(mockReferralStats.referral_link)}
                    className="px-4 py-3 bg-cyan-500 hover:bg-cyan-600 text-black font-bold rounded-lg transition-colors"
                  >
                    {copiedText === mockReferralStats.referral_link ? <FaCheck /> : <FaCopy />}
                  </button>
                </div>
              </div>
              
              {/* QR Code Placeholder */}
              <div className="text-center">
                <label className="text-sm text-gray-400 uppercase tracking-wide mb-2 block">QR Code</label>
                <div className="flex justify-center">
                  <div className="w-32 h-32 bg-white rounded-lg flex items-center justify-center">
                    <div className="text-black text-xs text-center p-2">
                      QR Code<br />
                      <span className="text-[8px]">{mockReferralStats.referral_link.slice(-8)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Referred Users List */}
          <motion.div
            className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 rounded-lg bg-gray-500/20 border border-gray-500/50">
                <FaUserPlus className="text-gray-400 text-xl" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-white uppercase tracking-wide">YOUR NETWORK</h3>
                <p className="text-xs text-gray-400 font-mono">REFERRED USERS</p>
              </div>
            </div>
            
            <div className="space-y-3">
              {mockReferredUsers.map((user, index) => (
                <motion.div
                  key={user.id}
                  className="flex items-center justify-between p-3 bg-black/30 rounded-lg"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-cyan-500 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-sm">{user.username.slice(0, 2).toUpperCase()}</span>
                    </div>
                    <div>
                      <div className="font-bold text-white">{user.username}</div>
                      <div className="text-xs text-gray-400">Joined {new Date(user.joined_at).toLocaleDateString()}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-yellow-300">{formatCurrency(user.earned_from)}</div>
                    <div className={`text-xs uppercase tracking-wide ${
                      user.status === 'active' ? 'text-green-400' : 'text-yellow-400'
                    }`}>
                      {user.status}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Rewards Info */}
        <motion.div
          className="bg-gradient-to-br from-yellow-500/20 to-orange-600/20 backdrop-blur-sm rounded-xl p-6 border border-yellow-500/30 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <FaGift className="text-4xl text-yellow-400 mx-auto mb-4" />
          <h3 className="text-xl font-bold text-yellow-300 mb-2 uppercase tracking-wide">REFERRAL REWARDS</h3>
          <p className="text-gray-300 mb-4">
            Earn <span className="text-yellow-300 font-bold">$5.00</span> for each friend who joins and 
            <span className="text-yellow-300 font-bold"> 10%</span> of their earnings forever!
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="bg-black/30 p-3 rounded-lg">
              <div className="text-yellow-300 font-bold">Instant Bonus</div>
              <div className="text-gray-300">$5.00 per referral</div>
            </div>
            <div className="bg-black/30 p-3 rounded-lg">
              <div className="text-yellow-300 font-bold">Lifetime Commission</div>
              <div className="text-gray-300">10% of their earnings</div>
            </div>
            <div className="bg-black/30 p-3 rounded-lg">
              <div className="text-yellow-300 font-bold">No Limits</div>
              <div className="text-gray-300">Unlimited referrals</div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default FriendsTabContent;
