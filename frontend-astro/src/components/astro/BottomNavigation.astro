---
/**
 * @file BottomNavigation.astro
 * @description Optimized bottom navigation for Telegram Mini App
 * Converted from React to Astro for better performance and smaller bundle
 */

export interface Props {
  activeTab: 'home' | 'earn' | 'friends' | 'wallet' | 'premium';
  className?: string;
}

const { activeTab, className = '' } = Astro.props;

// Navigation items configuration
const navItems = [
  {
    id: 'home',
    label: 'Home',
    path: '/dashboard/home',
    icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6'
  },
  {
    id: 'earn',
    label: 'Earn',
    path: '/dashboard/earn',
    icon: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1'
  },
  {
    id: 'friends',
    label: 'Friends',
    path: '/dashboard/friends',
    icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z'
  },
  {
    id: 'wallet',
    label: 'Wallet',
    path: '/dashboard/wallet',
    icon: 'M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z'
  },
  {
    id: 'premium',
    label: 'Premium',
    path: '/dashboard/premium',
    icon: 'M13 10V3L4 14h7v7l9-11h-7z'
  },
  {
    id: 'verse',
    label: 'Verse',
    path: '/verse',
    icon: 'M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064'
  }
];
---

<nav 
  class={`relative ${className}`}
  role="navigation"
  aria-label="Main navigation"
>
  <div class="relative h-full">
    <!-- Optimized shine effect with CSS-only animation -->
    <div class="absolute top-0 inset-x-0 h-0.5 bg-gradient-to-r from-transparent via-accent-500/10 to-transparent">
      <div class="absolute inset-0 flex justify-center">
        <div class="w-1/3 h-full relative overflow-hidden">
          <div class="absolute top-0 right-0 w-1/2 h-full bg-gradient-to-l from-transparent via-white/10 to-transparent animate-shine-left" />
        </div>
        <div class="w-1/3 h-full relative overflow-hidden">
          <div class="absolute top-0 left-0 w-1/2 h-full bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shine-right" />
        </div>
      </div>
    </div>

    <div class="container mx-auto px-2 h-full">
      <div class="flex justify-around items-center h-full">
        {navItems.map(item => {
          const isActive = item.id !== 'verse' && item.id === activeTab;
          return (
            <a
              href={item.path}
              class={`
                relative flex flex-col items-center justify-center h-full w-full group
                transition-all duration-200 no-underline
                ${isActive 
                  ? 'text-accent-400' 
                  : 'text-white/60 hover:text-white active:text-white/80'
                }
              `}
              aria-selected={isActive}
              aria-label={`Navigate to ${item.label}`}
            >
              <!-- Active indicator background -->
              {isActive && (
                <div class="absolute inset-0 bg-gradient-to-t from-accent-500/15 via-transparent to-transparent rounded-lg opacity-100" />
              )}

              <!-- Icon container -->
              <div class="relative mb-1 transition-transform duration-200 group-hover:scale-110 group-active:scale-95">
                <svg 
                  class={`w-4 h-4 transition-colors duration-200 ${
                    isActive 
                      ? 'text-accent-400 drop-shadow-[0_0_3px_rgba(168,85,247,0.4)]' 
                      : 'text-white/60 group-hover:text-white/80'
                  }`}
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path d={item.icon} />
                </svg>
                
                <!-- Active glowing indicator -->
                {isActive && (
                  <div class="absolute -bottom-2 left-1/2 -translate-x-1/2 w-1 h-1 bg-accent-300 rounded-full shadow-[0_0_6px_2px_theme(colors.accent.300/0.7)]" />
                )}
              </div>
              
              <!-- Label -->
              <span class={`
                text-xs font-medium relative z-10 transition-colors duration-200
                ${isActive 
                  ? 'text-transparent bg-clip-text bg-gradient-to-r from-accent-200 to-accent-400' 
                  : 'text-white/60 group-hover:text-white/80'
                }
              `}>
                {item.label}
              </span>
            </a>
          );
        })}
      </div>
    </div>
  </div>
</nav>

<!-- Optimized CSS animations -->
<style>
  /* Ensure navigation stays on top */
  nav {
    z-index: 40;
  }
  
  /* Smooth transitions for mobile */
  a {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }
  
  /* Active state micro-interactions */
  a:active {
    transform: scale(0.98);
  }
  
  /* Backdrop blur support */
  @supports (backdrop-filter: blur(12px)) {
    nav {
      backdrop-filter: blur(12px);
    }
  }
  
  /* Optimize for Telegram WebApp */
  @media (max-width: 768px) {
    nav {
      -webkit-user-select: none;
      user-select: none;
    }
  }
</style>

<!-- Telegram WebApp optimized JavaScript -->
<script>
  // Optimize for Telegram WebApp environment
  document.addEventListener('DOMContentLoaded', () => {
    // Prevent default touch behaviors that might interfere with Telegram
    const navLinks = document.querySelectorAll('nav a');
    
    navLinks.forEach(link => {
      // Add haptic feedback for Telegram WebApp
      link.addEventListener('click', (e) => {
        // Telegram WebApp haptic feedback
        if (window.Telegram?.WebApp?.HapticFeedback) {
          window.Telegram.WebApp.HapticFeedback.impactOccurred('light');
        }
        
        // Smooth navigation for better UX
        e.preventDefault();
        const href = link.getAttribute('href');
        if (href) {
          // Use Telegram WebApp navigation if available
          if (window.Telegram?.WebApp) {
            window.location.href = href;
          } else {
            window.location.href = href;
          }
        }
      });
      
      // Touch feedback
      link.addEventListener('touchstart', () => {
        link.style.transform = 'scale(0.95)';
      });
      
      link.addEventListener('touchend', () => {
        link.style.transform = '';
      });
    });
  });
</script>
