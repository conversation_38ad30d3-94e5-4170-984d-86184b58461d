/* All @import statements must be at the top */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
/* @import './fonts.css'; */
/* @import './cyberpunk-theme.css'; */

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    box-sizing: border-box;
  }
  
  html {
    font-family: 'Inter', system-ui, sans-serif;
    -webkit-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    /* Telegram Mini App optimizations */
    -webkit-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;

    /* Safe area support */
    --safe-area-inset-top: env(safe-area-inset-top);
    --safe-area-inset-bottom: env(safe-area-inset-bottom);
    --safe-area-inset-left: env(safe-area-inset-left);
    --safe-area-inset-right: env(safe-area-inset-right);

    /* Telegram WebApp CSS variables */
    --tg-viewport-height: var(--tg-viewport-height, 100vh);
    --tg-viewport-stable-height: var(--tg-viewport-stable-height, 100vh);
    --tg-safe-area-inset-top: var(--tg-safe-area-inset-top, 0px);
    --tg-safe-area-inset-bottom: var(--tg-safe-area-inset-bottom, 0px);
    --tg-safe-area-inset-left: var(--tg-safe-area-inset-left, 0px);
    --tg-safe-area-inset-right: var(--tg-safe-area-inset-right, 0px);
    --tg-content-safe-area-inset-top: var(--tg-content-safe-area-inset-top, 0px);
    --tg-content-safe-area-inset-bottom: var(--tg-content-safe-area-inset-bottom, 0px);
    --tg-content-safe-area-inset-left: var(--tg-content-safe-area-inset-left, 0px);
    --tg-content-safe-area-inset-right: var(--tg-content-safe-area-inset-right, 0px);
  }
  
  body {
    margin: 0;
    padding: 0;
    background: #000000;
    color: #ffffff;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;

    /* Mobile optimizations */
    -webkit-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;

    /* Prevent pull-to-refresh on mobile */
    overscroll-behavior-y: contain;

    /* Telegram Mini App height */
    min-height: var(--tg-viewport-height, 100vh);
    height: var(--tg-viewport-height, 100vh);
  }
  
  /* Telegram WebApp specific styles */
  body.telegram-web-app {
    background: var(--tg-theme-bg-color, #000000);
    color: var(--tg-theme-text-color, #ffffff);
  }

  /* Performance optimizations for low-end devices */
  @media (max-width: 768px) {
    * {
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
    }

    /* Reduce animations on low-performance devices */
    @media (prefers-reduced-motion: reduce) {
      *,
      *::before,
      *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
      }
    }
  }

  /* Fullscreen mode support */
  html.tg-fullscreen {
    --tg-viewport-height: 100vh;
    --tg-viewport-stable-height: 100vh;
  }

  html.tg-fullscreen body {
    height: 100vh;
    min-height: 100vh;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 4px;
  }
  
  ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
  }
  
  ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
  }
}

@layer components {
  /* Button styles */
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-black;
  }
  
  .btn-primary {
    @apply bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 focus:ring-purple-500;
  }
  
  .btn-secondary {
    @apply bg-gray-800 text-white border border-gray-600 hover:bg-gray-700 focus:ring-gray-500;
  }
  
  .btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
  }
  
  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }
  
  .btn-disabled {
    @apply opacity-50 cursor-not-allowed;
  }
  
  /* Card styles */
  .card {
    @apply bg-gradient-to-br from-gray-900/50 to-gray-800/50 rounded-xl border border-gray-600/20 backdrop-blur-sm;
  }
  
  .card-premium {
    @apply bg-gradient-to-br from-purple-900/50 to-blue-900/50 border-purple-500/20;
  }
  
  .card-success {
    @apply bg-gradient-to-br from-green-900/50 to-emerald-900/50 border-green-500/20;
  }
  
  /* Input styles */
  .input {
    @apply w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent;
  }
  
  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-600 border-t-purple-500;
  }
  
  /* Gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent;
  }
  
  /* Glass effect */
  .glass {
    @apply backdrop-blur-md bg-white/10 border border-white/20;
  }
  
  /* Tab navigation */
  .tab-nav {
    @apply flex bg-gray-900/50 rounded-xl p-1 backdrop-blur-sm;
  }
  
  .tab-item {
    @apply flex-1 py-2 px-4 text-center rounded-lg transition-all duration-200 text-sm font-medium;
  }
  
  .tab-item-active {
    @apply bg-purple-600 text-white;
  }
  
  .tab-item-inactive {
    @apply text-gray-400 hover:text-white hover:bg-gray-800/50;
  }
}

@layer utilities {
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  /* Safe area utilities for mobile */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .safe-left {
    padding-left: env(safe-area-inset-left);
  }
  
  .safe-right {
    padding-right: env(safe-area-inset-right);
  }
  
  /* Text utilities */
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  }
  
  /* Backdrop utilities */
  .backdrop-blur-strong {
    backdrop-filter: blur(20px);
  }

  /* Shine animation utilities */
  .animate-shine-left {
    animation: shine-left 3s ease-in-out infinite;
    animation-delay: 0s;
  }

  .animate-shine-right {
    animation: shine-right 3s ease-in-out infinite;
    animation-delay: 1.5s;
  }
}

/* Keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes shine-left {
  0% { transform: translateX(100%); }
  100% { transform: translateX(-100%); }
}

@keyframes shine-right {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .mobile-optimized {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    color-scheme: dark;
  }
}
