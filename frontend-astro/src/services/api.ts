/**
 * @file api.ts
 * @description API service for VIPVerse Telegram Mini App
 * Replaces axios with fetch for better compatibility with Astro
 */

import { telegramWebApp } from '../utils/telegramWebApp';

// Types
export interface User {
  id: number;
  username: string;
  email?: string;
  telegram_photo_url?: string;
  wallet_balance: number;
  total_passive_hourly_income: number;
  last_passive_claim_at?: string;
}

export interface UserStats {
  wallet_balance: number;
  total_accumulated_card_profit: number;
  total_passive_hourly_income: number;
  active_subscriptions: number;
  total_earned: number;
  pending_rewards: number;
}

export interface Card {
  id: number;
  name: string;
  level: number;
  profit_per_hour: number;
  upgrade_cost: number;
  image?: string;
  category: string;
  is_owned: boolean;
}

export interface Task {
  id: number;
  title: string;
  description: string;
  reward: number;
  status: 'available' | 'in_progress' | 'completed';
  type: 'daily' | 'social' | 'achievement';
  requirements?: any;
}

export interface ApiResponse<T> {
  data: T;
  status: string;
  message?: string;
}

export interface HealthCheck {
  status: string;
  timestamp: string;
  version: string;
}

// API Error handling
export class APIError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string
  ) {
    super(message);
    this.name = 'APIError';
  }
}

// API Configuration
const API_BASE_URL = import.meta.env.PUBLIC_API_URL || 'https://dev.atlasvip.cloud/api';

const getBaseURL = (): string => {
  if (typeof window !== 'undefined') {
    // Client-side: use the current domain
    return `${window.location.protocol}//${window.location.host}/api`;
  }
  // Server-side: use development URL
  return API_BASE_URL;
};

// Create axios instance
const createApiClient = (): AxiosInstance => {
  const client = axios.create({
    baseURL: getBaseURL(),
    timeout: API_CONFIG.TIMEOUT,
    headers: {
      'Content-Type': 'application/json',
    },
    withCredentials: true, // Important for cookie-based auth
  });

  // Request interceptor
  client.interceptors.request.use(
    (config) => {
      // Add any auth headers if needed
      const token = localStorage.getItem('auth_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Response interceptor
  client.interceptors.response.use(
    (response: AxiosResponse) => {
      return response;
    },
    (error) => {
      // Handle common errors
      if (error.response?.status === 401) {
        // Unauthorized - redirect to login
        if (typeof window !== 'undefined') {
          window.location.href = '/';
        }
      }
      return Promise.reject(error);
    }
  );

  return client;
};

// API Client
export const apiClient = createApiClient();

// API Service Class
export class ApiService {
  private client: AxiosInstance;

  constructor() {
    this.client = apiClient;
  }

  // Health Check
  async healthCheck(): Promise<HealthCheck> {
    const response = await this.client.get<HealthCheck>('/health');
    return response.data;
  }

  // User endpoints
  async getCurrentUser(): Promise<User> {
    const response = await this.client.get<User>('/me');
    return response.data;
  }

  async getDashboardStats(): Promise<DashboardStats> {
    const response = await this.client.get<DashboardStats>('/dashboard/stats');
    return response.data;
  }

  // Card endpoints
  async claimAllProfits(): Promise<{ success: boolean; amount: number }> {
    const response = await this.client.post<{ success: boolean; amount: number }>('/cards/claim-all');
    return response.data;
  }

  // VPN endpoints
  async getVpnStatus(): Promise<any> {
    const response = await this.client.get('/vpn/status');
    return response.data;
  }

  // Tasks endpoints
  async getTasks(): Promise<any[]> {
    const response = await this.client.get('/tasks');
    return response.data;
  }

  async completeTask(taskId: number): Promise<any> {
    const response = await this.client.post(`/tasks/${taskId}/complete`);
    return response.data;
  }

  // Referral endpoints
  async getReferralStats(): Promise<any> {
    const response = await this.client.get('/referrals/stats');
    return response.data;
  }

  // Generic request method
  async request<T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    endpoint: string,
    data?: any
  ): Promise<T> {
    const response = await this.client.request<T>({
      method,
      url: endpoint,
      data,
    });
    return response.data;
  }
}

// Export singleton instance
export const apiService = new ApiService();

// Utility functions for error handling
export const handleApiError = (error: any): string => {
  if (error.response?.data?.detail) {
    return error.response.data.detail;
  }
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  if (error.message) {
    return error.message;
  }
  return 'An unexpected error occurred';
};

export const isApiError = (error: any): boolean => {
  return error.response && error.response.status >= 400;
};

// Mock data for development/testing
export const mockUser: User = {
  id: 1,
  username: 'TestUser',
  email: '<EMAIL>',
  telegram_photo_url: 'test-avatar',
  wallet_balance: 125.50,
  total_passive_hourly_income: 2.5,
  last_passive_claim_at: new Date().toISOString(),
};

export const mockDashboardStats: DashboardStats = {
  active_subscriptions: 2,
  total_data_used: 1024,
  total_data_limit: 10240,
  wallet_balance: 125.50,
  total_accumulated_card_profit: 45.75,
  total_passive_hourly_income: 2.5,
  last_passive_claim_at: new Date().toISOString(),
};

// Development mode helpers
export const isDevelopment = (): boolean => {
  return import.meta.env.DEV || import.meta.env.VITE_ENVIRONMENT === 'development';
};

export const shouldUseMockData = (): boolean => {
  return isDevelopment() && import.meta.env.VITE_USE_MOCK_DATA === 'true';
};
