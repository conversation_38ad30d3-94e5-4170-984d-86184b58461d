---
/**
 * @file DashboardLayout.astro
 * @description Main dashboard layout for Astro, handling navigation and tab structure
 * Converted from React DashboardLayout.tsx to leverage Astro's islands architecture
 */

export interface Props {
  title: string;
  description?: string;
  activeTab?: 'home' | 'earn' | 'friends' | 'wallet' | 'premium';
}

const { 
  title, 
  description = "VIPVerse Dashboard - Manage your VIP experience", 
  activeTab = 'home' 
} = Astro.props;

// Get user data from middleware (temporarily disabled for testing)
// const user = Astro.locals.user;
// const isAuthenticated = Astro.locals.isAuthenticated;

// Redirect if not authenticated (temporarily disabled)
// if (!isAuthenticated) {
//   return Astro.redirect('/');
// }

// Import necessary components
import BaseLayout from './BaseLayout.astro';
import Header from '../components/astro/Header.astro';
import BottomNavigation from '../components/react/BottomNavigation';
import DashboardBackground from '../components/react/DashboardBackground';
---

<BaseLayout title={title} description={description} requireAuth={true}>
  <!-- Dashboard Background (React Island) -->
  <DashboardBackground client:load />
  
  <!-- Main Dashboard Container -->
  <div 
    id="dashboard-container" 
    class="relative flex flex-col h-screen max-h-screen overflow-hidden bg-black"
    style="height: 100vh; max-height: 100vh;"
  >
    <!-- Header (Astro Component) -->
    <Header
      activeTab={activeTab}
      class="fixed inset-x-0 z-10 h-[64px] bg-black/70 backdrop-blur-md shadow-lg shadow-black/40"
      style="top: env(safe-area-inset-top, 0px);"
    />

    <!-- Main Content Area -->
    <main 
      class="flex-1 overflow-hidden relative"
      style="padding-top: 64px; padding-bottom: 64px;"
    >
      <div class="h-full flex flex-col">
        <!-- Tab Content Slot -->
        <slot />
      </div>
    </main>

    <!-- Bottom Navigation (React Island) -->
    <BottomNavigation
      activeTab={activeTab}
      client:load
      className="fixed bottom-0 inset-x-0 z-10 h-[64px] bg-black/80 backdrop-blur-md shadow-[0_-4px_16px_rgba(0,0,0,0.5)]"
      style={{bottom: 'env(safe-area-inset-bottom, 0px)'}}
    />
  </div>

  <!-- Telegram WebApp Viewport Management -->
  <script>
    // Telegram WebApp viewport management
    if (window.Telegram?.WebApp) {
      const tg = window.Telegram.WebApp;
      
      // Expand to full height
      tg.expand();
      
      // Disable vertical swipes if available
      if (typeof tg.disableVerticalSwipes === 'function') {
        tg.disableVerticalSwipes();
      }
      
      // Handle viewport changes
      const handleViewportChange = () => {
        const height = tg.viewportStableHeight || tg.viewportHeight || window.innerHeight;
        const container = document.getElementById('dashboard-container');
        if (container) {
          container.style.height = `${height}px`;
          container.style.maxHeight = `${height}px`;
        }
      };
      
      // Initial setup
      handleViewportChange();
      
      // Listen for viewport changes
      tg.onEvent('viewportChanged', handleViewportChange);
      
      // Cleanup on page unload
      window.addEventListener('beforeunload', () => {
        tg.offEvent('viewportChanged', handleViewportChange);
      });
    }
    
    // Fallback for non-Telegram environments
    const handleResize = () => {
      const container = document.getElementById('dashboard-container');
      if (container) {
        container.style.height = `${window.innerHeight}px`;
        container.style.maxHeight = `${window.innerHeight}px`;
      }
    };
    
    window.addEventListener('resize', handleResize);
    window.addEventListener('beforeunload', () => {
      window.removeEventListener('resize', handleResize);
    });
  </script>

  <!-- Dashboard-specific styles -->
  <style>
    /* Ensure proper viewport handling */
    #dashboard-container {
      height: 100vh;
      height: 100dvh; /* Dynamic viewport height for mobile */
      max-height: 100vh;
      max-height: 100dvh;
    }
    
    /* Safe area handling for iOS */
    @supports (padding: env(safe-area-inset-top)) {
      #dashboard-container {
        padding-top: env(safe-area-inset-top);
        padding-bottom: env(safe-area-inset-bottom);
      }
    }
    
    /* Prevent overscroll on mobile */
    body {
      overscroll-behavior: none;
      -webkit-overflow-scrolling: touch;
    }
    
    /* Hide scrollbars but keep functionality */
    main {
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE and Edge */
    }
    
    main::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }
  </style>
</BaseLayout>
