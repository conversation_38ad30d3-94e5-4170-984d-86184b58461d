import { defineMiddleware } from 'astro:middleware';

function getApiBaseUrl(): string {
  if (typeof window === 'undefined') {
    return process.env.API_URL || 'https://dev.atlasvip.cloud';
  }
  return import.meta.env.VITE_API_URL || 'https://dev.atlasvip.cloud';
}

export const onRequest = defineMiddleware(async (context, next) => {
  const { url, request, cookies, redirect, locals } = context;
  
  // Public routes that don't require authentication
  const publicRoutes = [
    '/',
    '/new-user-setup',
    '/_astro',
    '/favicon.svg',
  ];
  
  const isPublicRoute = publicRoutes.some(route => 
    url.pathname === route || url.pathname.startsWith(route)
  );
  
  if (isPublicRoute) {
    return next();
  }
  
  try {
    // Extract auth cookies
    const authToken = cookies.get('auth_token')?.value;
    const refreshToken = cookies.get('refresh_token')?.value;
    
    if (!authToken) {
      return redirect('/');
    }
    
    // Verify session with backend
    const response = await fetch(`${getApiBaseUrl()}/auth/verify-session`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': request.headers.get('cookie') || '',
      },
      credentials: 'include',
    });
    
    if (response.ok) {
      const data = await response.json();
      if (data.status === 'authenticated') {
        // Store user data in locals for use in pages
        locals.user = data.data;
        locals.isAuthenticated = true;
        return next();
      }
    }
    
    // If verification fails, try to refresh token
    if (refreshToken) {
      const refreshResponse = await fetch(`${getApiBaseUrl()}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': request.headers.get('cookie') || '',
        },
        credentials: 'include',
      });
      
      if (refreshResponse.ok) {
        // Token refreshed successfully, continue
        const userData = await refreshResponse.json();
        locals.user = userData.data;
        locals.isAuthenticated = true;
        return next();
      }
    }
    
  } catch (error) {
    console.error('Authentication middleware error:', error);
  }
  
  // Authentication failed, redirect to landing
  return redirect('/');
});
